import requests
import json
import sys

# 测试搜索API
def test_search_api():
    # url = "http://127.0.0.1:8000/api/v1/search"
    url = "http://copilot.csvw.com/rag_service/api/v1/search"
    
    # 测试用例1: 标准请求
    payload1 = {
        "text": "油底壳重复渗漏",
        "top_k": 6,
        "collection": "vw_tsip", # table_name
        "database": "VAS", # database_name
        "embedding_type": "azure-openai",
        }
    
    
    # 测试所有用例
    test_cases = [
        ("标准请求", payload1, "application/json")
        # ("简化请求", payload2, "application/json"),
        # ("双引号JSON字符串", payload3, "application/json"),
        # ("单引号JSON字符串", payload4, "text/plain"),
        # ("数字类型top_k", payload5, "application/json"),
        # ("字符串类型top_k", payload6, "application/json")
    ]
    
    for name, payload, content_type in test_cases:
        print(f"\n\n测试用例: {name}")
        print(f"请求体: {payload}")
        print(f"Content-Type: {content_type}")
        
        try:
            headers = {"Content-Type": content_type}
            
            # 如果是字典，转换为JSON字符串
            if isinstance(payload, dict):
                payload_str = json.dumps(payload, ensure_ascii=False)
            else:
                payload_str = payload
                
            print(f"发送的实际请求体: {payload_str}")
            
            response = requests.post(url, data=payload_str, headers=headers)
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {response.headers}")
            print(f"响应体: {response.text}")
            
            if response.status_code == 200:
                print("✅ 测试成功!")
            else:
                print("❌ 测试失败!")
        except Exception as e:
            print(f"❌ 测试出错: {e}")

if __name__ == "__main__":
    import time
    t1 = time.time()
    test_search_api()
    t2 = time.time()
    print("总耗时：", t2-t1)